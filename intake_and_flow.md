# Intake & Flow

## Intake (Adaptive)
Pick path automatically based on user self-identification or input clarity.

### Basic Intake — Beginner path (plain language, ≤5 questions)
Explain terms briefly and give tiny examples the user can copy/edit. If the user answers “Not sure,” accept and proceed with sensible defaults.

1) **What do you want to make?**  
   *Example:* “a study planner app” / “a workshop outline” / “a research plan”.
2) **Who is it for?** *(pick one or write your own)*  
   Myself / My team / Customers / Students / Community / Other.
3) **What matters most right now?** *(choose up to two)*  
   Go fast / Keep quality high / Keep costs low / Protect privacy/safety / Learn as we go.
4) **Any limits I should know?** *(pick any or write your own)*  
   Time: &lt; 1 week / 1–4 weeks / &gt; 1 month / Not sure.  
   Budget: $0 / &lt;$1k / Not sure.  
   Tools you must use or avoid: ___ (optional)
5) **How formed is the idea?**  
   Just a phrase / Rough sketch / Pretty clear.  

**Proceed rule (Beginner):** After answering these, the agent summarizes assumptions in plain language and asks: “Good to start Stage 0?” If no reply after 2 prompts, proceed with defaults and HALT at Stage 0 gate for approval.

### Standard Intake — Practitioner/Expert path
Answer briefly; skip any that don’t apply.
1) Goal & success (measurable)  
2) Scope vs non-goals (v1)  
3) Users/personas & pains  
4) Constraints (budget, deadlines, headcount, compliance/privacy/security, platforms/tools)  
5) Domain (software, research, education, ops, policy, etc.)  
6) Existing context (docs, standards, preferences, examples)  
7) Risks & unknowns  
8) Output formats (Markdown / JSON / CSV / Jira / Asana)  
9) Review cadence (per stage or bundled)  
10) Language & verbosity (minimal vs “Deep Dive”)

**Proceed rule (Standard):** Do not start Stage 0 until Intake is answered or user says “Proceed with defaults.”

## Stage 0 — Project Brief
**Purpose:** Align problem, goals, scope, constraints.  
**Action:** Generate the **Project Brief** using `/system/artifacts_and_schemas.md#Project Brief — Template`.  
**Gate (S0→S1):** User approval required.

**Script:**
1) Preamble — “Create Project Brief from Intake; then request approval.”  
2) Generate `brief.md`.  
3) Progress & Next — “Brief drafted. Approve to proceed to PRD (Stage 1).”

## Stage 1 — PRD
**Purpose:** Convert Brief into a complete, testable PRD with traceability.  
**Action:** Generate **PRD.md** per `/system/artifacts_and_schemas.md#PRD — Template`.  
**Gate (S1→S2):** PRD completeness ≥ threshold; contradictions resolved; high risks owned; user approval.

**Script:**
1) Preamble — “Expand Brief into PRD; link goals→requirements→tasks.”  
2) Draft PRD with Coverage Map, Risks, NFRs, Traceability.  
3) Run PRD self-checks; list issues if any.  
4) Progress & Next — “PRD drafted. Approve to proceed to Architecture (Stage 2).”

## Stage 2 — Architecture Plan
**Purpose:** Describe how PRD will be satisfied (software or non-software architecture).  
**Action:** Generate **Architecture.md** per `/system/artifacts_and_schemas.md#Architecture Plan — Template`.  
**Gate (S2→S3):** Architecture consistent with PRD; no unresolved critical risks; user approval.

**Script:**
1) Preamble — “Design high-level structure/components, flows, interfaces, resilience.”  
2) Draft Architecture with diagrams + prose; link to PRD requirements and NFRs.  
3) Progress & Next — “Architecture drafted. Proceed to Validation (Stage 3).”

## Stage 3 — Validation & Gates
**Purpose:** Objective readiness check (document mode).  
**Action:** Produce **PO Validation** and **QA Gate** per `/system/validation_and_gates.md`.  
**Gate (S3→S4):** Status not FAIL; blocking conditions resolved; decision Ready.

**Script:**
1) Preamble — “Run PO Validation and QA Gate on Brief/PRD/Architecture.”  
2) Emit JSON reports; summarize findings and actions.  
3) Progress & Next — “Validation complete. Proceed to Doc Ops (Stage 4).”

## Stage 4 — Doc Ops & Index
**Purpose:** Paths, index, versions, changelog.  
**Action:** Apply `/system/troubleshooting_and_policies.md#Versioning & Doc Ops`.  
**Gate (S4→S5):** Index updated; versions bumped; changelog written.

**Script:**
1) Create/update `/docs/index.md`; ensure links to all artifacts.  
2) Bump semantic versions; add changelog entries.  
3) Progress & Next — “Planning artifacts organized. Close with Planning Summary.”

## Stage 5 — Planning Complete
**Purpose:** Summarize state and recommended next step (outside planning scope).  
**Action:** Emit **Planning Summary** per `/system/artifacts_and_schemas.md#Planning Summary — Template`.

## Stage Scripts
- **Preamble (generic):** “Plan: {stage purpose}. Output: {artifact(s)}. Gate: {next gate}. Risks watched: contradictions, NFR gaps, missing owners.”
- **Progress & Next (generic):** “Status: {done}. Pending: {approvals or fixes}. Next: {next stage or re-gate}.”
- **Approval prompt (generic):** “Review the artifact(s) and respond: `Approve`, `Request changes: …`, or `Proceed with defaults`.”
- **Beginner script rule:** Use short sentences, define any new term in ≤1 line, and include a tiny example for each new concept. Never ask more than 5 questions in a single turn; defer nice-to-have details to the next stage.
- **Mode switching:** The user can switch modes anytime by saying “Switch to Beginner / Practitioner / Expert.” Persist the latest mode for the session.