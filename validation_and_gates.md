# file path: /system/validation_and_gates.md

# Validation & Gates

## PO Validation — Spec
Output a planning-mode validation of Brief, PRD, and Architecture.
```json
{
  "completeness": { "Brief": 0.0, "PRD": 0.0, "Architecture": 0.0 },
  "mvp_size": "S|M|L",
  "top_issues": [],
  "decision": "Ready|Needs-Refinement",
  "rationale": ""
}
````

**Completeness** considers: required sections present, traceability links populated, risks owned, NFRs addressed, contradictions none or resolved.

## QA Gate — Spec

```json
{
  "gate": "Planning",
  "status": "PASS|CONCERNS|FAIL|WAIVED",
  "findings": [],
  "blocking_conditions": [],
  "actions": []
}
```

**Deterministic rules (document mode):**

* **PASS** — all required sections present; no critical contradictions; high risks owned; NFR evidence noted; traceability present.
* **CONCERNS** — minor gaps; proceed only if user explicitly accepts.
* **FAIL** — missing critical sections; unresolved contradictions; risk thresholds exceeded; unsafe compliance posture.
* **WAIVED** — explicit user acceptance of a listed rule.

## Gate Logic & Thresholds

* S1→S2 only if PRD completeness ≥ threshold and high risks have owners/mitigations.
* S2→S3 only if Architecture maps to PRD and no critical risk remains.
* S3 passes only if status not **FAIL** and all blocking conditions are resolved.

## Pause & Stop Rules

**Pause** for approval on: Scope/Non-Goals, Constraints, Success Metrics, Legal/Compliance/Security, Third-party Dependencies, Irreversible Changes, Timeline shifts.
**Stop** when: contradictions affect safety/scope; required sections missing; gate = **FAIL**; legal/compliance unresolved.

## Metrics to Track (planning quality)

* Section coverage %, contradictions flagged, review rounds to approval, time-to-PRD, readiness decision rate, risk closure rate, NFR evidence coverage.