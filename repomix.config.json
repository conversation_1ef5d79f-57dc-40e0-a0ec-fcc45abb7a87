{"$schema": "https://repomix.com/schemas/latest/schema.json", "input": {"maxFileSize": 52428800}, "output": {"filePath": "Project Crafter Deep Agent - Full repo - 1.md", "style": "markdown", "parsableStyle": false, "fileSummary": true, "directoryStructure": true, "files": true, "removeComments": false, "removeEmptyLines": false, "compress": false, "topFilesLength": 5, "showLineNumbers": false, "truncateBase64": false, "copyToClipboard": false, "tokenCountTree": false, "git": {"sortByChanges": true, "sortByChangesMaxCommits": 100, "includeDiffs": false, "includeLogs": false, "includeLogsCount": 50}}, "include": [], "ignore": {"useGitignore": true, "useDefaultPatterns": true, "customPatterns": []}, "security": {"enableSecurityCheck": true}, "tokenCount": {"encoding": "o200k_base"}}