# file path: /system/artifacts_and_schemas.md

# Artifacts & Schemas

## Project Brief — Template
# Project Brief
**schema_version** — `1.1`
**Problem** — concise description of the pain and context.
**Goals & Success Metrics** — measurable outcomes and targets.
**In-Scope** — boundaries for v1.
**Non-Goals** — explicitly out for v1.
**Users / Personas** — roles, needs, constraints.
**Constraints** — deadlines, budget, compliance/privacy/security, platforms/tools.
**Initial Risks & Assumptions** — risks with owners; key assumptions to validate.
**Assumptions — New** — list newly introduced assumptions this stage.
**Assumptions — Resolved** — list assumptions closed or replaced this stage.
**Decision Log (initial)** — decisions with rationale and date.

## PRD — Template
# Product Requirements Document
**schema_version** — `1.1`
**Problem**
**Goals & Non-Goals**
**Personas & User Stories / Use Cases**
**Requirements**
- **Functional Requirements (FRs)** — numbered, testable.
- **Non-Functional Requirements (NFRs)** — reliability, performance, security/privacy, operability, maintainability (adapt per domain).

**Flows**
```mermaid
%% Keep each diagram focused (flowchart/sequence/state). Provide a 2–3 line description under each.
````

**Flow Descriptions** — brief prose explaining key paths and edge cases.
**Data / Information Considerations** — classification, privacy, retention (or domain equivalent).
**Metrics** — leading and lagging indicators linked to Goals.
**Risks** — probability×impact, owner, mitigation, status.
**Dependencies** — vendors, data sources, approvals.
**Open Questions**
**Timeline & Releases** — planning-grade.
**Accessibility & i18n / domain accessibility**
**Observability / Validation Plan** — how outcomes will be verified.
**Maintenance / Support** — ownership beyond delivery.
**Appendix & Glossary**

### Assumptions Tracking
**Assumptions — New**
**Assumptions — Resolved**

> Beginner note: when generating this PRD in Beginner mode, auto-append a short **Glossary** of any terms used
> (each term defined in ≤1 sentence) and 1 tiny example for the most important section the user chose.

### Coverage Map (trace goals → requirements → tasks)

| Goal | Requirement ID | Verification Method | Risk Link | Task Link |

## Architecture Plan — Template

# Architecture Plan
**schema_version** — `1.1`

**Architectural Goals & Constraints** — align with PRD NFRs and constraints.
**Context & High-Level Structure** — diagram + prose.

```mermaid
%% High-level system/process view (e.g., C4-style or domain equivalent)
```

**Components / Modules (or Process Stages)** — responsibilities and interfaces.
**Data / Information Flows (or Artifact Flows)** — inputs, outputs, stores, retention.
**Interfaces & Hand-offs** — internal/external; roles; RACI where applicable.
**Security / Privacy Posture** — controls, threat considerations (planning level).
**Compliance & Audit** — standards and evidence strategy if relevant.
**Failure Modes & Resilience** — fault scenarios and mitigations.
**Test / Validation Alignment** — how architecture enables verification.
**Assumptions & Decisions** — list with rationale.
**Architecture Risks & Mitigations** — owners and status.

### Assumptions Tracking
**Assumptions — New**
**Assumptions — Resolved**

## Traceability — Templates

### Requirement ↔︎ Verification ↔︎ Architecture

| Requirement ID | Verification Method | Architecture Element | Evidence |

### Goal ↔︎ Requirement ↔︎ Task

| Goal | Requirement ID | Task ID/Link |

## Risk Profile — Template

| Risk | Probability | Impact | Owner | Mitigation | Status |

## NFR Assessment — Template

* **Performance** — target(s), evidence, gaps
* **Security/Privacy** — data classes, controls, gaps
* **Reliability/Continuity** — SLOs, fallback, gaps
* **Operability/Maintainability (or domain equivalent)** — runbooks/ownership, gaps

## Planning Summary — Template

```json
{
  "versions": { "PRD": "1.0.0", "Architecture": "1.0.0" },
  "approvals": [],
  "open_questions": [],
  "top_risks": [],
  "next_step": "Recommend moving to execution planning or research tasks."
}
```

## Changelog — Template

## [1.0.0] — 2025-09-01

* Initial planning artifacts created.
* Rationale: Aligned with Intake and approvals.
* Author: Planning Agent

## Mermaid Diagram Policy

* Default **hybrid**: provide a Mermaid diagram **and** a 2–3 line description.
* If the user opts out, provide description-only.
* Keep diagrams small and focused per flow or component cluster.